# OS generated files
.DS_Store
Thumbs.db

# Editor directories and files
.vscode/
.idea/
*.sublime-workspace
*.sublime-project

# Log files
*.log

# Cache and temp files
*.cache
*.tmp

# PHPStorm
*.iml

# Environment files
.env
.env.*

# Backup, swap, and sample files
*.bak
*.swp
*.sample

# User uploads (uncomment if you have a local uploads dir)
# /static/uploads/

# Documentation and notes (uncomment if not needed in production)
README.md
*.md

# Node modules (if any JS tooling is added)
node_modules/

# Ignore compiled CSS/JS maps (if generated)
/static/css/*.map
/static/js/*.map

# Composer dependencies

# PHP session, cache, and upload directories
/session/
/cache/
/uploads/

# Webhook logs or temp files
/webhook/*.log
/webhook/*.tmp

# Ignore PHP error logs
php_error.log

initialize.php
rzp.csv
