<?php
// Test Google Form submission
echo "<h2>Google Form Submission Test</h2>";

// Test data
$test_data = [
    'entry.2137584627' => 'Test User',     
    'entry.1048443022' => '<EMAIL>',
    'entry.298622297' => '1234567890',
    'entry.334303626' => '100',
    'entry.2014993418' => 'ABCDE1234F',
    'entry.1303449869' => 'Test Address',
    'entry.356075776' => 'Test Message',
    'entry.1387618563' => 'order_test123',
    'entry.767127390' => 'pay_test123',
    'entry.1764429049' => 'captured'
];

$google_form_url = 'https://docs.google.com/forms/d/e/1FAIpQLSeWOil4y9_JMSrofmT8INUzcBSsftSA2CinLHepRzRGo3LdFA/formResponse';

echo "<h3>Testing Google Form Submission</h3>";
echo "<p>Form URL: <code>$google_form_url</code></p>";
echo "<p>Test Data:</p>";
echo "<pre>" . print_r($test_data, true) . "</pre>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $google_form_url);
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($test_data));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_VERBOSE, true);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);
$info = curl_getinfo($ch);
curl_close($ch);

echo "<h3>Results:</h3>";
echo "<p><strong>HTTP Code:</strong> $http_code</p>";

if ($curl_error) {
    echo "<p><strong>cURL Error:</strong> $curl_error</p>";
} else {
    echo "<p><strong>cURL Error:</strong> None</p>";
}

echo "<p><strong>Response Headers and Body:</strong></p>";
echo "<pre>" . htmlspecialchars($response) . "</pre>";

echo "<h3>cURL Info:</h3>";
echo "<pre>" . print_r($info, true) . "</pre>";

// Check if submission was successful
if ($http_code == 200 && strpos($response, 'Your response has been recorded') !== false) {
    echo "<div style='color: green; font-weight: bold;'>✅ Google Form submission appears successful!</div>";
} elseif ($http_code == 200) {
    echo "<div style='color: orange; font-weight: bold;'>⚠️ HTTP 200 received but response doesn't contain success message</div>";
} else {
    echo "<div style='color: red; font-weight: bold;'>❌ Google Form submission failed</div>";
}

echo "<h3>Troubleshooting Tips:</h3>";
echo "<ul>";
echo "<li>Check if the Google Form URL is correct and accessible</li>";
echo "<li>Verify that the entry field IDs match your Google Form</li>";
echo "<li>Ensure the Google Form accepts responses from external sources</li>";
echo "<li>Check if there are any required fields missing</li>";
echo "</ul>";
?>
