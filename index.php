<?php 
include('include/initialize.php');
// Determine which page to load
$page = isset($_GET['p']) ? $_GET['p'] : 'home';
$folder = "page/";
$file = glob($folder . "*.php");
$file_name = $folder . $page . ".php";

// Set default SEO values for missing/404 or if not set by the page
if (!in_array($file_name, $file)) {
    $page_title = "404 Not Found | Unnyanpath Foundation";
    $meta_description = "The page you are looking for does not exist. Discover Unnyanpath Foundation's mission and explore our real stories of change in Uttar Pradesh.";
} else {
    // Optionally, set a generic default for pages that don't set their own
    $page_title = isset($page_title) ? $page_title : null;
    $meta_description = isset($meta_description) ? $meta_description : null;
}
?>
<!DOCTYPE html>
<html lang="en" itemscope itemtype="https://schema.org/WebPage">
<?php include('include/header.php') ?>

<body>
      <!-- Google tag (gtag.js) -->
      <script async src="https://www.googletagmanager.com/gtag/js?id=G-SXN3X8JX5W"></script>
      <script>
            window.dataLayer = window.dataLayer || [];

            function gtag() {
                  dataLayer.push(arguments);
            }
            gtag('js', new Date());

            gtag('config', 'G-SXN3X8JX5W');
      </script>
      
      <!-- Preloader Start -->
      <div class="preloader" role="status" aria-label="Page loading">
            <div class="loading-container">
                  <div class="loading"></div>
                  <div id="loading-icon"><img src="<?= base_url ?>static/images/logo/loader.svg" alt="Loading..."></div>
            </div>
      </div>
      <!-- Preloader End -->
      <main role="main" id="main-content" aria-label="Main Content">
            <?php 
            include('include/top_nav_bar.php');

            if (in_array($file_name, $file)) {
                  include($file_name);
            } else {
                  include 'page/404.php';
            }
            include('include/footer.php')
            ?>
      </main>
</body>

</html>