<?php
// Test image accessibility for email
?>
<!DOCTYPE html>
<html>
<head>
    <title>Image Test</title>
</head>
<body>
    <h2>Testing Email Images</h2>
    
    <h3>1. Mail Logo Test</h3>
    <img src="https://unnyanpathfoundation.in/static/images/logo/mail-logo.png" alt="Mail Logo" style="height:64px;">
    <p>URL: https://unnyanpathfoundation.in/static/images/logo/mail-logo.png</p>
    
    <h3>2. Square Logo Test</h3>
    <img src="https://unnyanpathfoundation.in/static/images/logo/squre-logo.png" alt="Square Logo" style="height:64px;">
    <p>URL: https://unnyanpathfoundation.in/static/images/logo/squre-logo.png</p>
    
    <h3>3. Regular Logo Test</h3>
    <img src="https://unnyanpathfoundation.in/static/images/logo/logo.svg" alt="Logo SVG" style="height:64px;">
    <p>URL: https://unnyanpathfoundation.in/static/images/logo/logo.svg</p>
    
    <h3>4. Direct Access Test</h3>
    <p>Try accessing directly:</p>
    <ul>
        <li><a href="https://unnyanpathfoundation.in/static/images/logo/mail-logo.png" target="_blank">Mail Logo PNG</a></li>
        <li><a href="https://unnyanpathfoundation.in/static/images/logo/squre-logo.png" target="_blank">Square Logo PNG</a></li>
        <li><a href="https://unnyanpathfoundation.in/static/images/logo/logo.svg" target="_blank">Logo SVG</a></li>
    </ul>
    
    <h3>5. Email Template Test</h3>
    <div style="max-width:600px;margin:0 auto;background:#F8F8F8;border-radius:14px;padding:36px 28px;font-family:Arial,sans-serif;color:#224520;box-shadow:0 4px 16px rgba(34,69,32,0.10);border:1.5px solid #e0e0e0;">
        <div style="text-align:center;margin-bottom:28px;">
            <img src="https://unnyanpathfoundation.in/static/images/logo/mail-logo.png" alt="UnnyanPath Foundation Logo" style="height:64px;margin-bottom:14px;">
            <h2 style="margin:0;color:#224520;font-size:2rem;">UnnyanPath Foundation</h2>
        </div>
        <p>This is how the email template should look with the logo.</p>
    </div>
    
    <script>
        // Test image loading with JavaScript
        function testImageLoad(url, name) {
            const img = new Image();
            img.onload = function() {
                console.log(`✅ ${name} loaded successfully`);
                document.getElementById('results').innerHTML += `<p style="color:green;">✅ ${name} loaded successfully</p>`;
            };
            img.onerror = function() {
                console.log(`❌ ${name} failed to load`);
                document.getElementById('results').innerHTML += `<p style="color:red;">❌ ${name} failed to load</p>`;
            };
            img.src = url;
        }
        
        // Test all images
        testImageLoad('https://unnyanpathfoundation.in/static/images/logo/mail-logo.png', 'Mail Logo PNG');
        testImageLoad('https://unnyanpathfoundation.in/static/images/logo/squre-logo.png', 'Square Logo PNG');
        testImageLoad('https://unnyanpathfoundation.in/static/images/logo/logo.svg', 'Logo SVG');
    </script>
    
    <h3>6. JavaScript Test Results</h3>
    <div id="results"></div>
    
    <h3>7. Troubleshooting</h3>
    <p>If images don't load:</p>
    <ul>
        <li>Check .htaccess hotlink protection rules</li>
        <li>Verify file permissions (should be 644)</li>
        <li>Test direct URL access</li>
        <li>Check server error logs</li>
        <li>Verify HTTPS certificate</li>
    </ul>
</body>
</html>
