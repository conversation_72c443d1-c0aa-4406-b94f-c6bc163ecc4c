<?php
/**
 * Email Template Helper
 * Provides email-compatible HTML templates
 */

class EmailTemplate {
    
    /**
     * Generate social media links section (email-compatible)
     */
    public static function getSocialMediaSection() {
        return '
        <div style="text-align:center;margin-top:20px;padding:20px 0;border-top:1px solid #e0e0e0;">
            <h3 style="font-size:16px;color:#224520;margin:0 0 15px 0;font-family:Arial,sans-serif;">Connect with us</h3>
            
            <!-- Social Media Buttons (Text-based for better email compatibility) -->
            <table style="margin:0 auto;border-collapse:collapse;font-family:Arial,sans-serif;">
                <tr>
                    <td style="padding:3px;">
                        <a href="https://whatsapp.com/channel/0029VbAyWCWJ93wbTTvz612x" 
                           style="display:inline-block;background:#25D366;color:#ffffff;padding:8px 12px;border-radius:4px;text-decoration:none;font-size:11px;font-weight:bold;font-family:Arial,sans-serif;" 
                           target="_blank">📱 WhatsApp</a>
                    </td>
                    <td style="padding:3px;">
                        <a href="https://www.linkedin.com/company/unnyanpath-foundation/" 
                           style="display:inline-block;background:#0077B5;color:#ffffff;padding:8px 12px;border-radius:4px;text-decoration:none;font-size:11px;font-weight:bold;font-family:Arial,sans-serif;" 
                           target="_blank">💼 LinkedIn</a>
                    </td>
                    <td style="padding:3px;">
                        <a href="https://twitter.com/upf_foundation" 
                           style="display:inline-block;background:#1DA1F2;color:#ffffff;padding:8px 12px;border-radius:4px;text-decoration:none;font-size:11px;font-weight:bold;font-family:Arial,sans-serif;" 
                           target="_blank">🐦 Twitter</a>
                    </td>
                </tr>
                <tr>
                    <td style="padding:3px;">
                        <a href="https://www.facebook.com/people/Unnyanpath-Foundation/100089804272261/" 
                           style="display:inline-block;background:#1877F2;color:#ffffff;padding:8px 12px;border-radius:4px;text-decoration:none;font-size:11px;font-weight:bold;font-family:Arial,sans-serif;" 
                           target="_blank">👥 Facebook</a>
                    </td>
                    <td style="padding:3px;">
                        <a href="https://www.instagram.com/unnyanpathfoundation" 
                           style="display:inline-block;background:#E4405F;color:#ffffff;padding:8px 12px;border-radius:4px;text-decoration:none;font-size:11px;font-weight:bold;font-family:Arial,sans-serif;" 
                           target="_blank">📸 Instagram</a>
                    </td>
                    <td style="padding:3px;">
                        <a href="https://www.youtube.com/@unnyanpathfoundation" 
                           style="display:inline-block;background:#FF0000;color:#ffffff;padding:8px 12px;border-radius:4px;text-decoration:none;font-size:11px;font-weight:bold;font-family:Arial,sans-serif;" 
                           target="_blank">📺 YouTube</a>
                    </td>
                </tr>
            </table>
            
            <!-- Fallback text links for email clients that don\'t support buttons -->
            <div style="margin-top:15px;font-size:12px;color:#666666;font-family:Arial,sans-serif;">
                <p style="margin:5px 0;">
                    <a href="https://whatsapp.com/channel/0029VbAyWCWJ93wbTTvz612x" style="color:#25D366;text-decoration:none;">WhatsApp</a> | 
                    <a href="https://www.linkedin.com/company/unnyanpath-foundation/" style="color:#0077B5;text-decoration:none;">LinkedIn</a> | 
                    <a href="https://twitter.com/upf_foundation" style="color:#1DA1F2;text-decoration:none;">Twitter</a> | 
                    <a href="https://www.facebook.com/people/Unnyanpath-Foundation/100089804272261/" style="color:#1877F2;text-decoration:none;">Facebook</a> | 
                    <a href="https://www.instagram.com/unnyanpathfoundation" style="color:#E4405F;text-decoration:none;">Instagram</a> | 
                    <a href="https://www.youtube.com/@unnyanpathfoundation" style="color:#FF0000;text-decoration:none;">YouTube</a>
                </p>
            </div>
        </div>';
    }
    
    /**
     * Generate email header with logo
     */
    public static function getEmailHeader($title = 'UnnyanPath Foundation') {
        return '
        <div style="text-align:center;padding:20px 0;background:#f8f9fa;">
            <div style="max-width:600px;margin:0 auto;background:#ffffff;border-radius:8px;overflow:hidden;box-shadow:0 2px 10px rgba(0,0,0,0.1);">
                <div style="background:#224520;padding:20px;text-align:center;">
                    <h1 style="color:#ffffff;margin:0;font-size:24px;font-family:Arial,sans-serif;">' . htmlspecialchars($title) . '</h1>
                    <p style="color:#ffffff;margin:5px 0 0 0;font-size:14px;font-family:Arial,sans-serif;">Making a difference in Uttar Pradesh</p>
                </div>
                <div style="padding:30px;">';
    }
    
    /**
     * Generate email footer
     */
    public static function getEmailFooter() {
        return '
                </div>
                <div style="background:#f8f9fa;padding:20px;text-align:center;border-top:1px solid #e0e0e0;">
                    <div style="font-size:12px;color:#666666;font-family:Arial,sans-serif;margin-bottom:10px;">
                        This is an automated email. Please do not reply to this email.<br>
                        &copy; ' . date("Y") . ' UnnyanPath Foundation. All rights reserved.
                    </div>
                    ' . self::getSocialMediaSection() . '
                </div>
            </div>
        </div>';
    }
    
    /**
     * Generate complete email template
     */
    public static function generateEmail($title, $content) {
        return self::getEmailHeader($title) . $content . self::getEmailFooter();
    }
    
    /**
     * Generate donation receipt email
     */
    public static function generateDonationReceipt($donorData) {
        $content = '
        <div style="font-family:Arial,sans-serif;line-height:1.6;color:#333333;">
            <p style="font-size:16px;margin-bottom:20px;">Dear ' . htmlspecialchars($donorData['name']) . ',</p>
            
            <p style="margin-bottom:20px;">We have received your donation and truly appreciate your support!</p>
            
            <div style="background:#f8f9fa;padding:20px;border-radius:6px;margin:20px 0;">
                <h3 style="color:#224520;margin:0 0 15px 0;font-size:18px;">Your Donation Details:</h3>
                <table style="width:100%;border-collapse:collapse;font-size:14px;">
                    <tr><td style="padding:5px 0;font-weight:bold;">Name:</td><td style="padding:5px 0;">' . htmlspecialchars($donorData['name']) . '</td></tr>
                    <tr><td style="padding:5px 0;font-weight:bold;">Email:</td><td style="padding:5px 0;">' . htmlspecialchars($donorData['email']) . '</td></tr>
                    <tr><td style="padding:5px 0;font-weight:bold;">Phone:</td><td style="padding:5px 0;">' . htmlspecialchars($donorData['phone']) . '</td></tr>
                    ' . (!empty($donorData['pan']) ? '<tr><td style="padding:5px 0;font-weight:bold;">PAN:</td><td style="padding:5px 0;">' . htmlspecialchars($donorData['pan']) . '</td></tr>' : '') . '
                    <tr><td style="padding:5px 0;font-weight:bold;">Amount:</td><td style="padding:5px 0;color:#224520;font-weight:bold;">₹' . htmlspecialchars($donorData['amount']) . '</td></tr>
                    ' . (!empty($donorData['address']) ? '<tr><td style="padding:5px 0;font-weight:bold;">Address:</td><td style="padding:5px 0;">' . htmlspecialchars($donorData['address']) . '</td></tr>' : '') . '
                    ' . (!empty($donorData['message']) ? '<tr><td style="padding:5px 0;font-weight:bold;">Message:</td><td style="padding:5px 0;">' . htmlspecialchars($donorData['message']) . '</td></tr>' : '') . '
                    <tr><td style="padding:5px 0;font-weight:bold;">Order ID:</td><td style="padding:5px 0;">' . htmlspecialchars($donorData['order_id']) . '</td></tr>
                    <tr><td style="padding:5px 0;font-weight:bold;">Payment ID:</td><td style="padding:5px 0;">' . htmlspecialchars($donorData['payment_id']) . '</td></tr>
                </table>
            </div>
            
            <p style="margin:20px 0;">Your contribution helps us continue our mission of creating positive change in communities across Uttar Pradesh.</p>
            
            <div style="text-align:center;margin:30px 0;">
                <a href="https://unnyanpathfoundation.in" 
                   style="display:inline-block;background:#224520;color:#ffffff;padding:12px 24px;border-radius:6px;text-decoration:none;font-weight:bold;font-family:Arial,sans-serif;">
                   Visit Our Website
                </a>
            </div>
            
            <p style="margin-top:20px;font-size:14px;color:#666666;">
                Thank you for being part of our mission to make a difference!
            </p>
        </div>';
        
        return self::generateEmail('Thank you for your donation!', $content);
    }
}
?>
