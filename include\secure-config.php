<?php
/**
 * Secure Configuration Loader
 * Loads configuration from environment variables or .env file
 */

// Load environment variables from .env file if it exists
function loadEnvFile($filePath) {
    if (!file_exists($filePath)) {
        return false;
    }
    
    $lines = file($filePath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue; // Skip comments
        }
        
        list($name, $value) = explode('=', $line, 2);
        $name = trim($name);
        $value = trim($value);
        
        if (!array_key_exists($name, $_SERVER) && !array_key_exists($name, $_ENV)) {
            putenv(sprintf('%s=%s', $name, $value));
            $_ENV[$name] = $value;
            $_SERVER[$name] = $value;
        }
    }
    return true;
}

// Load .env file
$envFile = __DIR__ . '/../.env';
loadEnvFile($envFile);

// Helper function to get environment variable with fallback
function getEnv($key, $default = null) {
    $value = $_ENV[$key] ?? $_SERVER[$key] ?? getenv($key);
    return $value !== false ? $value : $default;
}

// Validate required environment variables
function validateRequiredEnvVars() {
    $required = [
        'RAZORPAY_KEY_ID',
        'RAZORPAY_KEY_SECRET',
        'MAIL_HOST',
        'MAIL_USERNAME',
        'MAIL_PASSWORD',
        'RECAPTCHA_SITE_KEY',
        'RECAPTCHA_SECRET_KEY'
    ];
    
    $missing = [];
    foreach ($required as $var) {
        if (empty(getEnv($var))) {
            $missing[] = $var;
        }
    }
    
    if (!empty($missing)) {
        error_log('Missing required environment variables: ' . implode(', ', $missing));
        if (getEnv('ENVIRONMENT') === 'development') {
            die('Missing required environment variables: ' . implode(', ', $missing));
        }
        return false;
    }
    return true;
}

// Validate environment variables
validateRequiredEnvVars();

// Define constants securely
if (!defined('RAZORPAY_KEY_ID'))
    define('RAZORPAY_KEY_ID', getEnv('RAZORPAY_KEY_ID'));
if (!defined('RAZORPAY_KEY_SECRET'))
    define('RAZORPAY_KEY_SECRET', getEnv('RAZORPAY_KEY_SECRET'));

if (!defined('MAIL_HOST'))
    define('MAIL_HOST', getEnv('MAIL_HOST'));
if (!defined('MAIL_USERNAME'))
    define('MAIL_USERNAME', getEnv('MAIL_USERNAME'));
if (!defined('MAIL_PASSWORD'))
    define('MAIL_PASSWORD', getEnv('MAIL_PASSWORD'));
if (!defined('MAIL_ENCRYPTION'))
    define('MAIL_ENCRYPTION', getEnv('MAIL_ENCRYPTION', 'ssl'));
if (!defined('MAIL_PORT'))
    define('MAIL_PORT', getEnv('MAIL_PORT', 465));

if (!defined('RECAPTCHA_SITE_KEY'))
    define('RECAPTCHA_SITE_KEY', getEnv('RECAPTCHA_SITE_KEY'));
if (!defined('RECAPTCHA_SECRET_KEY'))
    define('RECAPTCHA_SECRET_KEY', getEnv('RECAPTCHA_SECRET_KEY'));

if (!defined('base_url'))
    define('base_url', getEnv('BASE_URL', 'http://localhost:3000/'));
if (!defined('base_app'))
    define('base_app', str_replace('\\', '/', __DIR__) . '/');

// Security: Check if we're in production
if (!defined('IS_PRODUCTION'))
    define('IS_PRODUCTION', getEnv('ENVIRONMENT') === 'production');

// Security: Disable error display in production
if (IS_PRODUCTION) {
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
    error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED);
} else {
    ini_set('display_errors', 1);
    error_reporting(E_ALL);
}
?>
