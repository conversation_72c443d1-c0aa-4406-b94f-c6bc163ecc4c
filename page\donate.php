<?php
$page_title = "Donate | Unnyanpath Foundation - Support Our Mission";
$meta_description = "Support Unnyanpath Foundation's mission by making a donation. Your contribution helps us empower communities and promote education in Uttar Pradesh.";

if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
$csrf_token = $_SESSION['csrf_token'];
?>
<!-- Page Header Start -->
<div class="page-header parallaxie">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-12">
                <!-- Page Header Box Start -->
                <div class="page-header-box">
                    <h1 class="text-anime-style-2" data-cursor="-opaque">Donation</h1>
                    <nav class="wow fadeInUp">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="<?= base_url ?>home">home</a></li>
                            <li class="breadcrumb-item active" aria-current="page">donation</li>
                        </ol>
                    </nav>
                </div>
                <!-- Page Header Box End -->
            </div>
        </div>
    </div>
</div>
<!-- Page Header End -->

<!-- Page Donation Start -->
<div class="page-donation">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="donation-box">
                    <div class="section-title">
                        <h3 class="wow fadeInUp">donate now</h3>
                        <h2 class="text-anime-style-2" data-cursor="-opaque">Your donation</h2>
                        <p class="wow fadeInUp" data-wow-delay="0.2s">
                            Your donation is more than just financial support; it is a powerful act of kindness that drives meaningful change. Every contribution helps provide essential resources, support impactful programs, and empower communities in need.
                        </p>
                    </div>

                    <!-- Donation Form -->
                    <div class="donate-form campaign-donate-form">
                        <form id="donateForm">
                            <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrf_token); ?>">
                            <!-- Donation Amount -->
                            <div class="campaign-donate-value wow fadeInUp" data-wow-delay="0.4s">
                                <!-- Donation Amount -->
                                <div class="col-md-12 mb-4">
                                    <label><strong>Enter Amount (in ₹)</strong></label>
                                    <input type="number" name="donation_amount" id="donation_amount" class="form-control" placeholder="Enter amount" min="1" required>
                                </div>

                                <fieldset class="donate-value-box">
                                    <div class="donate-value">
                                        <input type="radio" id="value1" name="preset_amount" value="100">
                                        <label for="value1">₹ 100</label>
                                    </div>
                                    <div class="donate-value">
                                        <input type="radio" id="value2" name="preset_amount" value="500">
                                        <label for="value2">₹ 500</label>
                                    </div>
                                    <div class="donate-value">
                                        <input type="radio" id="value3" name="preset_amount" value="1000">
                                        <label for="value3">₹ 1,000</label>
                                    </div>
                                    <div class="donate-value">
                                        <input type="radio" id="value4" name="preset_amount" value="2000">
                                        <label for="value4">₹ 2,000</label>
                                    </div>
                                    <div class="donate-value">
                                        <input type="radio" id="value5" name="preset_amount" value="5000">
                                        <label for="value5">₹ 5,000</label>
                                    </div>
                                    <div class="donate-value">
                                        <input type="radio" id="value6" name="preset_amount" value="10000">
                                        <label for="value6">₹ 10,000</label>
                                    </div>
                                </fieldset>
                            </div>

                            <!-- Personal Info -->
                            <div class="donar-personal-info">
                                <div class="section-title">
                                    <h2 class="text-anime-style-2" data-cursor="-opaque">Personal <span>info</span></h2>
                                </div>
                                <div class="row wow fadeInUp" data-wow-delay="0.8s">
                                    <div class="row wow fadeInUp" data-wow-delay="0.8s">
                                        <div class="form-group col-md-6 mb-4">
                                            <input type="text" name="name" class="form-control" placeholder=" Enter Your Full Name" required>
                                        </div>
                                        <div class="form-group col-md-6 mb-4">
                                            <input type="email" name="email" class="form-control" placeholder="Enter Your E-mail" required>
                                        </div>
                                        <div class="form-group col-md-6 mb-4">
                                            <input type="text" name="text" class="form-control" placeholder="Enter Your PAN No." required>
                                        </div>
                                        <div class="form-group col-md-6 mb-4">
                                            <input type="text" name="phone" class="form-control" placeholder="Enter Your Phone No." required>
                                        </div>
                                        <div class="form-group col-md-6 mb-5">
                                            <textarea name="message" class="form-control" rows="2" placeholder=" Enter YourAddress"></textarea>
                                        </div>
                                        <div class="form-group col-md-6 mb-5">
                                            <textarea name="message" class="form-control" rows="2" placeholder="Write a message (optional)"></textarea>
                                        </div>
                                    </div>
                                </div>

                                <!-- reCAPTCHA Widget -->
                                <div class="form-group col-md-12 mb-3">
                                    <div class="g-recaptcha" data-sitekey="<?php echo RECAPTCHA_SITE_KEY; ?>"></div>
                                </div>
                                <!-- Submit Button -->
                                <div class="form-group col-md-12">
                                    <button type="submit" class="btn-default"><span>Donate</span></button>
                                    <div id="msgSubmit" class="h3 hidden"></div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <!-- Donation Form End -->
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Page Donation End -->

<script src="https://checkout.razorpay.com/v1/checkout.js"></script>
<script src="https://www.google.com/recaptcha/api.js" async defer></script>
<script>
    document.querySelectorAll('input[name="preset_amount"]').forEach(function(radio) {
        radio.addEventListener('change', function() {
            document.getElementById('donation_amount').value = this.value;
        });
    });

    document.getElementById("donateForm").addEventListener("submit", function(e) {
        e.preventDefault();
        const form = e.target;
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalBtnText = submitBtn.textContent;
        submitBtn.disabled = true;
        submitBtn.textContent = "Processing...";

        // Get reCAPTCHA response and append to formData
        var recaptchaResponse = grecaptcha.getResponse();
        if (!recaptchaResponse) {
            Swal.fire({
                icon: "error",
                title: "reCAPTCHA Required",
                text: "Please complete the reCAPTCHA.",
                background: "#F8F8F8",
                color: "#224520",
                confirmButtonColor: "#f15e25",
            });
            submitBtn.textContent = originalBtnText;
            submitBtn.disabled = false;
            return;
        }
        formData.append('g-recaptcha-response', recaptchaResponse);

        fetch("../webhook/donation-webhook.php", {
                method: "POST",
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.order_id && data.razorpay_key) {
                    // Trigger Razorpay Checkout
                    var options = {
                        "key": data.razorpay_key,
                        "amount": data.amount,
                        "currency": data.currency,
                        "name": "UnnyanPath Foundation",
                        "description": "Donation",
                        "image": "/static/images/logo/squre-logo.svg",
                        "order_id": data.order_id,
                        "handler": function (response){
                            Swal.fire({
                                icon: "success",
                                title: "Thank you!",
                                html: `<div style='color:#224520;'>Payment successful.<br>Payment ID: <b>${response.razorpay_payment_id}</b><br>You will receive a receipt soon.</div>`,
                                background: "#F8F8F8",
                                color: "#224520",
                                confirmButtonColor: "#f15e25"
                            });
                            form.reset();
                            submitBtn.disabled = false;
                            submitBtn.textContent = "Donate Now";
                        },
                        "modal": {
                            "ondismiss": function() {
                                Swal.fire({
                                    icon: "info",
                                    title: "Payment Cancelled",
                                    text: "You cancelled the payment. No money was deducted.",
                                    background: "#F8F8F8",
                                    color: "#224520",
                                    confirmButtonColor: "#f15e25"
                                });
                                submitBtn.disabled = false;
                                submitBtn.textContent = "Donate Now";
                            },
                            "escape": true,
                            "backdropclose": true
                        },
                        "retry": {
                            "enabled": true,
                            "max_count": 2
                        }
                    };
                    var rzp1 = new Razorpay(options);
                    rzp1.open();
                } else {
                    Swal.fire({
                        icon: "error",
                        title: "Error",
                        text: data.error || "Submission failed. Please try again.",
                        background: "#F8F8F8",
                        color: "#224520",
                        confirmButtonColor: "#f15e25",
                    });
                }
            })
            .catch(() => {
                Swal.fire({
                    icon: "error",
                    title: "Error",
                    text: "Submission failed. Please try again.",
                    background: "#F8F8F8",
                    color: "#224520",
                    confirmButtonColor: "#f15e25",
                });
            })
            .finally(() => {
                submitBtn.textContent = originalBtnText;
                submitBtn.disabled = false;
            });
    });
</script>
