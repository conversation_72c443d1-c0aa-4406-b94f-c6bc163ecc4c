# Enhanced Security Configuration for UnnyanPath Foundation

# Enable URL rewriting
RewriteEngine On

# Security Headers
<IfModule mod_headers.c>
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options "nosniff"
    
    # Prevent clickjacking
    Header always set X-Frame-Options "DENY"
    
    # Enable XSS protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # Referrer Policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # HTTPS Strict Transport Security (only if using HTTPS)
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
    
    # Content Security Policy
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://www.google.com https://www.gstatic.com https://checkout.razorpay.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.razorpay.com https://www.google-analytics.com; frame-src https://www.google.com;"
    
    # Remove server signature
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# Hide server information
ServerTokens Prod

# Canonical HTTPS and non-www redirect
RewriteCond %{HTTPS} off [OR]
RewriteCond %{HTTP_HOST} ^www\.(.+)$ [NC]
RewriteRule ^ https://unnyanpathfoundation.in%{REQUEST_URI} [R=301,L]

# Block access to sensitive files and directories
<FilesMatch "\.(env|log|ini|conf|bak|sql|git)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Protect sensitive directories
<DirectoryMatch "(include|logs|vendor|webhook)">
    Order allow,deny
    Deny from all
</DirectoryMatch>

# Allow POST requests to webhook directory
RewriteCond %{REQUEST_METHOD} POST
RewriteCond %{REQUEST_URI} ^/webhook/
RewriteRule ^(.*)$ - [L]

# Block all direct access except POST to PHP files in sensitive folders
RewriteCond %{REQUEST_METHOD} !POST
RewriteRule ^(include|logs|vendor|webhook)/.*\.php$ https://unnyanpathfoundation.in/404 [NC,R=302,L]

# Block all other access to non-PHP files in sensitive backend and system folders
RewriteRule ^(vendor|webhook|logs|include)/.*\.(?!php$)[^/]+$ https://unnyanpathfoundation.in/404 [R=302,L]

# Rate limiting (basic)
<IfModule mod_evasive24.c>
    DOSHashTableSize    2048
    DOSPageCount        10
    DOSPageInterval     1
    DOSSiteCount        50
    DOSSiteInterval     1
    DOSBlockingPeriod   600
</IfModule>

# Prevent access to backup and temporary files
<FilesMatch "\.(bak|backup|old|tmp|temp|swp|~)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Prevent access to version control files
<FilesMatch "\.(git|svn|hg|bzr)">
    Order allow,deny
    Deny from all
</FilesMatch>

# Disable directory browsing
Options -Indexes

# Disable server-side includes
Options -Includes

# Disable CGI execution
Options -ExecCGI

# Prevent access to PHP configuration files
<Files "php.ini">
    Order allow,deny
    Deny from all
</Files>

# Rewrite URLs like /about to index.php?p=about
RewriteRule ^([a-zA-Z0-9\-]+)$ index.php?p=$1 [L,QSA]

# Optionally, handle URLs like /about/ (with trailing slash)
RewriteRule ^([a-zA-Z0-9\-]+)/$ index.php?p=$1 [L,QSA]

# Hotlink protection
RewriteCond %{HTTP_REFERER} !^https?://(www\.)?unnyanpathfoundation.in/.*$ [NC]
RewriteRule .+\.(jpg|jpeg|gif|png|bmp|tiff|avi|mpeg|mpg|wma|mov|zip|rar|exe|mp3|swf|psd|txt|html|htm|php)$ https://unnyanpathfoundation.in/static/images/logo/logo.png [R,NC,L]

# Custom error pages
ErrorDocument 400 https://unnyanpathfoundation.in/404
ErrorDocument 401 https://unnyanpathfoundation.in/404
ErrorDocument 403 https://unnyanpathfoundation.in/404
ErrorDocument 404 https://unnyanpathfoundation.in/404
ErrorDocument 500 https://unnyanpathfoundation.in/404

# Compress static files for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache static files
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>
