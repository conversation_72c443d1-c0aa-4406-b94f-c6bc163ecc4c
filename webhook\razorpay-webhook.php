<?php
// Razorpay Webhook Handler
// Receives payment notifications from <PERSON><PERSON><PERSON><PERSON>, verifies signature, and sends receipt email

// Check if this is a direct call from frontend or webhook from Razorpay
$is_frontend_call = isset($_SERVER['CONTENT_TYPE']) && strpos($_SERVER['CONTENT_TYPE'], 'application/json') !== false;

// Idempotency: prevent duplicate processing (only for actual webhooks)
$webhook_id = $_SERVER['HTTP_X_RAZORPAY_EVENT_ID'] ?? '';
$webhook_log_file = __DIR__ . '/../logs/razorpay-webhook-events.log';
if ($webhook_id && !$is_frontend_call) {
    $processed = file_exists($webhook_log_file) ? file($webhook_log_file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES) : [];
    if (in_array($webhook_id, $processed)) {
        http_response_code(200);
        echo 'Event already processed.';
        exit;
    }
}

require_once __DIR__ . '/../vendor/razorpay-php/Razorpay.php';
require_once __DIR__ . '/../vendor/phpmailer/src/Exception.php';
require_once __DIR__ . '/../vendor/phpmailer/src/PHPMailer.php';
require_once __DIR__ . '/../vendor/phpmailer/src/SMTP.php';
require_once __DIR__ . '/../include/initialize.php';
use Razorpay\Api\Api;
use Razorpay\Api\Errors\SignatureVerificationError;
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

// Razorpay API credentials
$razorpay_key_id = RAZORPAY_KEY_ID;
$razorpay_key_secret = RAZORPAY_KEY_SECRET;

try {
    $api = new Api($razorpay_key_id, $razorpay_key_secret);

    if ($is_frontend_call) {
        // Handle direct call from frontend
        $input = json_decode(file_get_contents('php://input'), true);

        // Verify payment with Razorpay
        $payment = $api->payment->fetch($input['razorpay_payment_id']);

        // Verify signature
        $attributes = [
            'razorpay_order_id' => $input['razorpay_order_id'],
            'razorpay_payment_id' => $input['razorpay_payment_id'],
            'razorpay_signature' => $input['razorpay_signature']
        ];
        $api->utility->verifyPaymentSignature($attributes);

        // Get payment details
        $email = $input['email'] ?? '';
        $name = $input['name'] ?? '';
        $amount = $input['amount'] ?? 0;
        $phone = $input['phone'] ?? '';
        $pan = '';
        $address = '';
        $message = '';
        $order_id = $input['razorpay_order_id'] ?? '';
        $payment_id = $input['razorpay_payment_id'] ?? '';
        $status = $payment['status'] ?? '';

    } else {
        // Handle traditional webhook from Razorpay
        $webhookBody = file_get_contents('php://input');
        $webhookSignature = $_SERVER['HTTP_X_RAZORPAY_SIGNATURE'] ?? '';

        // Verify webhook signature
        $api->utility->verifyWebhookSignature($webhookBody, $webhookSignature, $razorpay_key_secret);

        $payload = json_decode($webhookBody, true);
        if ($payload['event'] !== 'payment.captured') {
            http_response_code(200);
            echo 'Event not handled.';
            exit;
        }

        $payment = $payload['payload']['payment']['entity'];
        $email = $payment['email'] ?? '';
        $name = $payment['notes']['name'] ?? '';
        $amount = $payment['amount'] / 100;
        $phone = $payment['contact'] ?? '';
        $pan = $payment['notes']['pan'] ?? '';
        $address = $payment['notes']['address'] ?? '';
        $message = $payment['notes']['message'] ?? '';
        $order_id = $payment['order_id'] ?? '';
        $payment_id = $payment['id'] ?? '';
        $status = $payment['status'] ?? '';
    }

    // Process payment (common for both webhook and frontend calls)
    if ($status === 'captured' || $status === 'authorized') {
        // Submit to Google Form
        $google_form_url = 'https://docs.google.com/forms/d/e/1FAIpQLSeWOil4y9_JMSrofmT8INUzcBSsftSA2CinLHepRzRGo3LdFA/formResponse';
        $post_fields = [
            'entry.2137584627' => $name,     
            'entry.1048443022' => $email,
            'entry.298622297' => $phone,
            'entry.334303626' => $amount,
            'entry.2014993418' => $pan,
            'entry.1303449869' => $address,
            'entry.356075776' => $message,
            'entry.1387618563' => $order_id,
            'entry.767127390' => $payment_id,
            'entry.1764429049' => $status
        ];
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $google_form_url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($post_fields));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        $response = curl_exec($ch);
        curl_close($ch);

        // Send receipt email
        $mail = new PHPMailer(true);
        $mail->isSMTP();
        $mail->Host = MAIL_HOST;
        $mail->SMTPAuth = true;
        $mail->Username = MAIL_USERNAME;
        $mail->Password = MAIL_PASSWORD;
        $mail->SMTPSecure = MAIL_ENCRYPTION;
        $mail->Port = MAIL_PORT;
        
        $mail->setFrom(MAIL_USERNAME, 'UnnyanPath Donation Team');
        $mail->addAddress($email);
        $mail->addBCC('<EMAIL>'); // BCC to support

        $mail->isHTML(true);
        $mail->Subject = "Thank you for your donation to UnnyanPath Foundation!";
        $mail->Body = '
            <div style="max-width:600px;margin:0 auto;background:#F8F8F8;border-radius:14px;padding:36px 28px;font-family:Nunito Sans,Inter,sans-serif;color:#224520;box-shadow:0 4px 16px rgba(34,69,32,0.10);border:1.5px solid #e0e0e0;">
                <div style="text-align:center;margin-bottom:28px;">
                    <img src="https://unnyanpathfoundation.in/static/images/logo/mail-logo.png" alt="UnnyanPath Foundation Logo" style="height:64px;margin-bottom:14px;">
                    <h2 style="margin:0;font-family:Nunito Sans,sans-serif;color:#224520;font-size:2rem;letter-spacing:1px;">UnnyanPath Foundation</h2>
                </div>
                <p style="font-size:16px;line-height:1.8em;color:#224520;margin-bottom:18px;">Dear ' . htmlspecialchars($name) . ',<br><br>
                We have received your donation and truly appreciate your support.<br><br>
                <strong>Your Donation Details:</strong><br>
                <b>Name:</b> ' . htmlspecialchars($name) . '<br>
                <b>Email:</b> ' . htmlspecialchars($email) . '<br>
                <b>Phone:</b> ' . htmlspecialchars($phone) . '<br>
                <b>PAN:</b> ' . htmlspecialchars($pan) . '<br>
                <b>Amount:</b> ₹' . htmlspecialchars($amount) . '<br>
                <b>Address:</b> ' . nl2br(htmlspecialchars($address)) . '<br>
                <b>Message:</b> ' . nl2br(htmlspecialchars($message)) . '<br>
                <b>Order ID:</b> ' . htmlspecialchars($order_id) . '<br>
                <b>Payment ID:</b> ' . htmlspecialchars($payment_id) . '<br>
                </p>
                <div style="margin-top:32px;text-align:center;">
                    <a href="https://unnyanpathfoundation.in/" style="background:#224520;color:#fff;padding:14px 36px;border-radius:8px;text-decoration:none;font-weight:700;font-family:Nunito Sans,sans-serif;display:inline-block;font-size:1rem;transition:background 0.2s;">Visit Our Website</a>
                </div>
                <hr style="margin:36px 0 18px 0;border:0;border-top:1.5px solid #e0e0e0;">
                <div style="text-align:center;font-size:13px;color:#828282;">
                    This is an automated email. Please do not reply to this email.<br>
                    &copy; ' . date('Y') . ' UnnyanPath Foundation
                </div>
                <div style="text-align:center;">
                    <h3 style="font-size:1.1rem;color:#224520;margin-bottom:10px;">Follow us on</h3>
                    <a href="https://whatsapp.com/channel/0029VbAyWCWJ93wbTTvz612x" style="margin:0 6px;text-decoration:none;" target="_blank"><img src="https://unnyanpathfoundation.in/static/images/icon/whatsapp.png" alt="WhatsApp" height="28"></a>
                    <a href="https://www.linkedin.com/company/unnyanpath-foundation/" style="margin:0 6px;text-decoration:none;" target="_blank"><img src="https://unnyanpathfoundation.in/static/images/icon/linkedin.png" alt="LinkedIn" height="28"></a>
                    <a href="https://twitter.com/upf_foundation" style="margin:0 6px;text-decoration:none;" target="_blank"><img src="https://unnyanpathfoundation.in/static/images/icon/twitter.png" alt="Twitter" height="28"></a>
                    <a href="https://www.facebook.com/people/Unnyanpath-Foundation/100089804272261/" style="margin:0 6px;text-decoration:none;" target="_blank"><img src="https://unnyanpathfoundation.in/static/images/icon/facebook.png" alt="Facebook" height="28"></a>
                    <a href="https://www.instagram.com/unnyanpathfoundation" style="margin:0 6px;text-decoration:none;" target="_blank"><img src="https://unnyanpathfoundation.in/static/images/icon/instagram.png" alt="Instagram" height="28"></a>
                    <a href="https://www.youtube.com/@unnyanpathfoundation" style="margin:0 6px;text-decoration:none;" target="_blank"><img src="https://unnyanpathfoundation.in/static/images/icon/youtube.png" alt="YouTube" height="28"></a>
                    <a href="https://wa.me/919999999999" style="margin:0 6px;text-decoration:none;" target="_blank"><img src="https://unnyanpathfoundation.in/static/images/icon/whatsapp.png" alt="WhatsApp" height="28"></a>
                </div>
            </div>';
        $mail->send();
    }

    // After successful processing, log the event id (only for actual webhooks)
    if ($webhook_id && !$is_frontend_call) {
        file_put_contents($webhook_log_file, $webhook_id . "\n", FILE_APPEND | LOCK_EX);
    }

    http_response_code(200);
    echo 'Payment processed successfully.';
} catch (SignatureVerificationError $e) {
    http_response_code(400);
    echo 'Signature verification failed.';
} catch (Exception $e) {
    http_response_code(500);
    echo 'Error: ' . $e->getMessage();
}
