<?php
// Test frontend webhook call
echo "<h2>Frontend Webhook Call Test</h2>";

// Test data that would come from frontend
$test_webhook_data = [
    'razorpay_payment_id' => 'pay_test123456',
    'razorpay_order_id' => 'order_test123456',
    'razorpay_signature' => 'test_signature',
    'name' => 'Test User',
    'email' => '<EMAIL>',
    'phone' => '1234567890',
    'pan' => '**********',
    'address' => 'Test Address, Test City',
    'message' => 'Test donation message',
    'amount' => 100
];

echo "<h3>Test Data:</h3>";
echo "<pre>" . print_r($test_webhook_data, true) . "</pre>";

// Get the webhook URL
$webhook_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . 
               '://' . $_SERVER['HTTP_HOST'] . '/webhook/razorpay-webhook.php';

echo "<h3>Webhook URL:</h3>";
echo "<p><code>$webhook_url</code></p>";

echo "<h3>Sending Test Request...</h3>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $webhook_url);
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($test_webhook_data));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen(json_encode($test_webhook_data))
]);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);
curl_close($ch);

echo "<h3>Results:</h3>";
echo "<p><strong>HTTP Code:</strong> $http_code</p>";

if ($curl_error) {
    echo "<p><strong>cURL Error:</strong> $curl_error</p>";
} else {
    echo "<p><strong>cURL Error:</strong> None</p>";
}

echo "<p><strong>Response:</strong></p>";
echo "<pre>" . htmlspecialchars($response) . "</pre>";

// Check logs
$log_file = __DIR__ . '/../logs/donation-webhook-error.log';
if (file_exists($log_file)) {
    echo "<h3>Recent Log Entries:</h3>";
    $log_content = file_get_contents($log_file);
    $log_lines = explode("\n", $log_content);
    $recent_lines = array_slice($log_lines, -20); // Last 20 lines
    echo "<pre>" . htmlspecialchars(implode("\n", $recent_lines)) . "</pre>";
} else {
    echo "<h3>No log file found</h3>";
}

echo "<h3>Notes:</h3>";
echo "<ul>";
echo "<li>This test simulates what happens when the frontend calls the webhook after successful payment</li>";
echo "<li>The webhook should process the payment data and send email + submit to Google Form</li>";
echo "<li>Check the logs above to see what happened during processing</li>";
echo "<li>Note: This test uses fake Razorpay IDs, so signature verification will fail</li>";
echo "</ul>";
?>
