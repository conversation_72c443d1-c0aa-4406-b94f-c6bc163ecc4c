<?php
// Tool to help find correct Google Form entry field IDs
echo "<h2>Google Form Field ID Finder</h2>";

echo "<h3>Instructions to Find Correct Entry Field IDs:</h3>";
echo "<ol>";
echo "<li><strong>Open your Google Form</strong> in edit mode</li>";
echo "<li><strong>Click 'Preview'</strong> (eye icon) to view the form</li>";
echo "<li><strong>Right-click on the page</strong> and select 'View Page Source' or press Ctrl+U</li>";
echo "<li><strong>Search for 'entry.'</strong> in the source code</li>";
echo "<li><strong>Look for patterns like:</strong> <code>name=\"entry.1234567890\"</code></li>";
echo "<li><strong>Match each field</strong> with its corresponding entry ID</li>";
echo "</ol>";

echo "<h3>Current Entry IDs in Use:</h3>";
$current_entries = [
    'entry.2137584627' => 'Name',
    'entry.1048443022' => 'Email',
    'entry.298622297' => 'Phone',
    'entry.334303626' => 'Amount',
    'entry.2014993418' => 'PAN',
    'entry.1303449869' => 'Address',
    'entry.356075776' => 'Message',
    'entry.1387618563' => 'Order ID',
    'entry.767127390' => 'Payment ID',
    'entry.1764429049' => 'Status'
];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Entry ID</th><th>Field Purpose</th></tr>";
foreach ($current_entries as $entry_id => $purpose) {
    echo "<tr><td><code>$entry_id</code></td><td>$purpose</td></tr>";
}
echo "</table>";

echo "<h3>Alternative Method - Get Form Pre-fill URL:</h3>";
echo "<ol>";
echo "<li>Open your Google Form</li>";
echo "<li>Click the <strong>three dots menu</strong> (⋮) in the top right</li>";
echo "<li>Select <strong>'Get pre-filled link'</strong></li>";
echo "<li>Fill in some test data in each field</li>";
echo "<li>Click <strong>'Get link'</strong></li>";
echo "<li>Copy the URL and examine the entry IDs in it</li>";
echo "</ol>";

echo "<h3>Test Your Google Form Settings:</h3>";
echo "<p>Make sure your Google Form has these settings:</p>";
echo "<ul>";
echo "<li>✅ <strong>Accepts responses</strong> (not closed)</li>";
echo "<li>✅ <strong>Allows responses from anyone</strong> (not restricted to organization)</li>";
echo "<li>✅ <strong>No required sign-in</strong></li>";
echo "</ul>";

echo "<h3>Manual Test:</h3>";
echo "<p>Try submitting to your form manually using this form:</p>";

$google_form_url = 'https://docs.google.com/forms/d/e/1FAIpQLSeWOil4y9_JMSrofmT8INUzcBSsftSA2CinLHepRzRGo3LdFA/formResponse';
?>

<form method="POST" action="<?php echo $google_form_url; ?>" target="_blank">
    <table>
        <tr><td>Name:</td><td><input type="text" name="entry.2137584627" value="Test User"></td></tr>
        <tr><td>Email:</td><td><input type="email" name="entry.1048443022" value="<EMAIL>"></td></tr>
        <tr><td>Phone:</td><td><input type="text" name="entry.298622297" value="1234567890"></td></tr>
        <tr><td>Amount:</td><td><input type="number" name="entry.334303626" value="100"></td></tr>
        <tr><td>PAN:</td><td><input type="text" name="entry.2014993418" value="**********"></td></tr>
        <tr><td>Address:</td><td><input type="text" name="entry.1303449869" value="Test Address"></td></tr>
        <tr><td>Message:</td><td><input type="text" name="entry.356075776" value="Test Message"></td></tr>
        <tr><td>Order ID:</td><td><input type="text" name="entry.1387618563" value="order_test123"></td></tr>
        <tr><td>Payment ID:</td><td><input type="text" name="entry.767127390" value="pay_test123"></td></tr>
        <tr><td>Status:</td><td><input type="text" name="entry.1764429049" value="captured"></td></tr>
    </table>
    <br>
    <input type="submit" value="Test Submit to Google Form">
</form>

<?php
echo "<p><strong>Note:</strong> Click the button above to test if the form accepts submissions with current entry IDs.</p>";

echo "<h3>Check Google Form Responses:</h3>";
echo "<p>After testing, check your Google Form responses to see:</p>";
echo "<ul>";
echo "<li>If any responses were recorded</li>";
echo "<li>Which fields are being filled correctly</li>";
echo "<li>Which fields are missing or incorrect</li>";
echo "</ul>";

echo "<h3>Common Issues:</h3>";
echo "<ul>";
echo "<li><strong>Wrong Entry IDs:</strong> The most common issue - entry IDs change when form is modified</li>";
echo "<li><strong>Form Closed:</strong> Form not accepting responses</li>";
echo "<li><strong>Required Fields:</strong> Missing required fields in submission</li>";
echo "<li><strong>Field Type Mismatch:</strong> Sending text to number field, etc.</li>";
echo "</ul>";
?>
