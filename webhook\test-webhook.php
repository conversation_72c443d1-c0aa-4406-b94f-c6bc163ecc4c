<?php
// Test script to verify webhook functionality
require_once __DIR__ . '/../include/initialize.php';

echo "<h2>Razorpay Integration Test</h2>";

// Test 1: Check Razorpay credentials
echo "<h3>1. Testing Razorpay Credentials</h3>";
try {
    require_once __DIR__ . '/../vendor/razorpay-php/Razorpay.php';
    use Razorpay\Api\Api;
    
    $api = new Api(RAZORPAY_KEY_ID, RAZORPAY_KEY_SECRET);
    
    // Try to fetch a dummy order (this will fail but will test authentication)
    try {
        $orders = $api->order->all(['count' => 1]);
        echo "✅ Razorpay credentials are valid<br>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Authentication failed') !== false) {
            echo "❌ Razorpay authentication failed. Please check your API keys.<br>";
        } else {
            echo "✅ Razorpay credentials are valid (API accessible)<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Error loading Razorpay SDK: " . $e->getMessage() . "<br>";
}

// Test 2: Check file permissions
echo "<h3>2. Testing File Permissions</h3>";
$log_dir = __DIR__ . '/../logs';
if (is_writable($log_dir)) {
    echo "✅ Logs directory is writable<br>";
} else {
    echo "❌ Logs directory is not writable<br>";
}

// Test 3: Check webhook URL accessibility
echo "<h3>3. Webhook URLs</h3>";
$base_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'];
$webhook_url = $base_url . '/webhook/razorpay-webhook.php';
echo "Webhook URL: <code>$webhook_url</code><br>";
echo "Configure this URL in your Razorpay Dashboard under Webhooks<br>";

// Test 4: Check required constants
echo "<h3>4. Configuration Check</h3>";
$required_constants = ['RAZORPAY_KEY_ID', 'RAZORPAY_KEY_SECRET', 'MAIL_HOST', 'MAIL_USERNAME', 'MAIL_PASSWORD'];
foreach ($required_constants as $constant) {
    if (defined($constant) && !empty(constant($constant))) {
        echo "✅ $constant is configured<br>";
    } else {
        echo "❌ $constant is missing or empty<br>";
    }
}

echo "<h3>5. Next Steps</h3>";
echo "<ol>";
echo "<li>Ensure your Razorpay account is in live mode if you're using live keys</li>";
echo "<li>Configure the webhook URL in your Razorpay Dashboard</li>";
echo "<li>Enable the 'payment.captured' event in webhook settings</li>";
echo "<li>Test a small donation to verify the complete flow</li>";
echo "</ol>";
?>
